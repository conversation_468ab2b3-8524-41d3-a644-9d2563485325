import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/app-based.dart';
import 'package:stickyqrbusiness/@core/models/order.model.dart';
import 'package:stickyqrbusiness/@core/store/auth/auth_bloc.dart';
import 'package:stickyqrbusiness/@core/store/delivery-quote/delivery-quote.cubit.dart';
import 'package:stickyqrbusiness/@core/store/new-delivery/new-delivery.cubit.dart';
import 'package:stickyqrbusiness/@core/store/orders-active/orders-active.cubit.dart';
import 'package:stickyqrbusiness/@utils/currency-input-formatter/money-input-enums.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';

class ReviewQuoteWidget extends StatefulWidget {
  final Order order;
  const ReviewQuoteWidget({
    Key? key,
    required this.order,
  }) : super(key: key);

  @override
  State<ReviewQuoteWidget> createState() => _ReviewQuoteWidgetState();
}

class _ReviewQuoteWidgetState extends State<ReviewQuoteWidget> {
  late final l10n = context.l10n;
  // String selectedTip = r'$3.00';
  final TextEditingController customAmountController = TextEditingController();
  final FocusNode customAmountFocusNode = FocusNode();

  @override
  void dispose() {
    customAmountController.dispose();
    // customAmountFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: BlocListener<NewDeliveryCubit, NewDeliveryState>(
        listener: (context, state) {
          if (state.status == NewDeliveryStatus.Success) {
            context.read<OrdersActiveCubit>().getOrdersActive(loading: false);
            AppBased.toastSuccess(context, title: l10n.successfully);
            Navigator.pop(context, true);
          } else if (state.status == NewDeliveryStatus.Error) {
            if (state.errorMsg != null && state.errorMsg != '') {
              AppBased.toastError(context, title: state.errorMsg);
              context.read<NewDeliveryCubit>().onRemoveErrMsg('');
            }
          }
        },
        child: Scaffold(
          backgroundColor: Colors.white,
          resizeToAvoidBottomInset: true,
          appBar: _buildAppbar(),
          body: _buildBody(),
        ),
      ),
    );
  }

  AppBar _buildAppbar() {
    return AppBar(
      centerTitle: true,
      backgroundColor: Colors.white,
      elevation: 0,
      leading: Container(),
      title: Text(
        l10n.reviewQuote,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: IconButton(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: SvgPicture.asset(
              'assets/svgs/close-black.svg',
              width: 28,
              height: 28,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDeliverInfo() {
    return BlocBuilder<DeliveryQuoteCubit, DeliveryQuoteState>(
      builder: (context, state) {
        final timeZone = context.read<AuthBloc>().state.businessTimeZone;
        final expiresAt = state.quoteData?.quote?.expires;
        final expiresTimeFormat = DateTimeHelper.dateTimeLongFormat(
          expiresAt,
          timeZone: timeZone,
          isWeekDay: false,
          langLocale: Localizations.localeOf(context).languageCode,
        );
        final deliverByTime = state.quoteData?.quote?.dropoffEta;
        final deliverByTimeFormat = DateTimeHelper.dateTimeLongFormat(
          deliverByTime,
          timeZone: timeZone,
          isWeekDay: false,
          langLocale: Localizations.localeOf(context).languageCode,
        );
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.thisQuoteExpiresAt(expiresTimeFormat),
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 20),
            Text(
              l10n.deliverBy,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            Text(
              deliverByTimeFormat,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 20),
          ],
        );
      },
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDeliverInfo(),
                _buildTip(),
                const SizedBox(height: 24),
                Container(
                  height: 1,
                  color: const Color(0xFFEBEBEB),
                ),
                const SizedBox(height: 16),
                _buildSummary(),
                const SizedBox(height: 100),
              ],
            ),
          ),
        ),
        _buildBottom(),
      ],
    );
  }

  Widget _buildTip() {
    return BlocBuilder<NewDeliveryCubit, NewDeliveryState>(
      builder: (context, state) {
        final tip = state.tipSelected ?? '0';
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              l10n.addTip,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 8),
            DecoratedBox(
              decoration: BoxDecoration(
                color: const Color(0xFFE2E2E2),
                borderRadius: BorderRadius.circular(12),
              ),
              // height: 40,
              child: Row(
                children: [
                  _buildTipButton('1'),
                  _buildTipButton('3'),
                  _buildTipButton('5'),
                  _buildTipButton(l10n.newDeliveryCustom),
                ],
              ),
            ),
            if (tip == l10n.newDeliveryCustom) ...[
              const SizedBox(height: 16),
              _buildInputAmount(),
            ],
          ],
        );
      },
    );
  }

  Widget _buildTipButton(String tip) {
    final String currencyBusiness = widget.order.currencyCode ??
        context.read<AuthBloc>().state.currencyBusiness ??
        'USD';
    final isCurrencyVND = currencyBusiness == 'VND';
    final String currency = isCurrencyVND ? '₫' : r'$';
    final tipAmount = tip == l10n.newDeliveryCustom
        ? l10n.newDeliveryCustom
        : isCurrencyVND
            ? '${double.parse(tip).toStringAsFixed(2)}$currency'
            : '$currency${double.parse(tip).toStringAsFixed(2)}';

    return BlocBuilder<NewDeliveryCubit, NewDeliveryState>(
      builder: (context, state) {
        final bool isSelected = state.tipSelected == tip;

        final tipValue = _getTipAmountValue(tip);

        return Expanded(
          child: GestureDetector(
            onTap: () {
              // context.read<NewDeliveryCubit>().onChangedTip(tip);
              if (tip == l10n.newDeliveryCustom) {
                context.read<NewDeliveryCubit>()
                  ..onChangedTipSelect(tip)
                  ..onChangedTipAmount(0.00);
                // Auto focus khi chọn Custom
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  customAmountFocusNode.requestFocus();
                });
              } else {
                // Unfocus khi chọn tip khác
                customAmountFocusNode.unfocus();
                customAmountController.clear();
                context.read<NewDeliveryCubit>()
                  ..onChangedTipSelect(tip)
                  ..onChangedTipAmount(tipValue);
              }
            },
            child: Container(
              margin: const EdgeInsets.all(4),
              padding: const EdgeInsets.symmetric(vertical: 6),
              decoration: BoxDecoration(
                color:
                    isSelected ? const Color(0xFF2C2C2C) : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                tipAmount,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isSelected ? Colors.white : Colors.black,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInputAmount() {
    final String currencyBusiness = widget.order.currencyCode ??
        context.read<AuthBloc>().state.currencyBusiness ??
        'USD';
    final isCurrencyVND = currencyBusiness == 'VND';

    return BlocBuilder<NewDeliveryCubit, NewDeliveryState>(
      builder: (context, state) {
        final amountInput = state.tipAmount ?? 0;
        final currency = isCurrencyVND ? '₫' : r'$';

        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SizedBox(
              height: 48,
              child: TextFormField(
                initialValue: amountInput.toString(),
                cursorColor: AppColors.cursorColor,
                cursorWidth: 1,
                focusNode: customAmountFocusNode,
                autofocus: true,
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  if (isCurrencyVND)
                    CurrencyInputFormatter(
                      thousandSeparator: ThousandSeparator.Period,
                      mantissaLength: 0,
                    )
                  else
                    CurrencyInputFormatter(),
                ],
                onChanged: (String value) {
                  context
                      .read<NewDeliveryCubit>()
                      .onChangedTipAmount(double.parse(value));
                },
                textAlign: TextAlign.left,
                textAlignVertical: TextAlignVertical.center,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
                maxLength: 15,
                decoration: InputDecoration(
                  counterText: '',
                  contentPadding: const EdgeInsets.all(16),
                  hintText: l10n.billAmount,
                  hintStyle: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade400,
                    fontWeight: FontWeight.normal,
                  ),
                  prefixIconConstraints: const BoxConstraints(
                    maxWidth: 50,
                    minWidth: 36,
                  ),
                  prefixIcon: Container(
                    padding: const EdgeInsets.only(left: 16),
                    width: 16,
                    alignment: Alignment.centerLeft,
                    child: Text(
                      currency,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  filled: true,
                  fillColor: AppColors.lightPrimaryBackgroundColor,
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(
                      color: Color(0xFFEBEBEB),
                      width: 1,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(
                      color: Color(0xFF2C2C2C),
                      width: 1,
                    ),
                  ),
                  isDense: true,
                ),
              ),
            ),
            const SizedBox(height: 4),
          ],
        );
      },
    );
  }

  Widget _buildSummary() {
    return BlocBuilder<DeliveryQuoteCubit, DeliveryQuoteState>(
      builder: (context, quoteState) {
        final String currencyBusiness = quoteState.quoteData?.quote?.currency ??
            widget.order.currencyCode ??
            context.read<AuthBloc>().state.currencyBusiness ??
            'USD';
        final isCurrencyVND = currencyBusiness == 'VND';
        final String currency = isCurrencyVND ? '₫' : r'$';
        final fee = quoteState.quoteData?.quote?.feeDisplay ?? 0.0;
        return BlocBuilder<NewDeliveryCubit, NewDeliveryState>(
          builder: (context, deliveryState) {
            final tip = deliveryState.tipAmount ?? 0.0;
            final feeDisplay = isCurrencyVND
                ? '${fee.toStringAsFixed(2)}$currency'
                : '$currency${fee.toStringAsFixed(2)}';
            final tipDisplay = isCurrencyVND
                ? '${tip.toStringAsFixed(2)}$currency'
                : '$currency${tip.toStringAsFixed(2)}';
            final totalFare = fee + tip;
            final totalFareDisplay = isCurrencyVND
                ? '${totalFare.toStringAsFixed(2)}$currency'
                : '$currency${totalFare.toStringAsFixed(2)}';
            return Column(
              children: [
                _buildPriceRow(l10n.deliveryFee, feeDisplay),
                const SizedBox(height: 8),
                _buildPriceRow(l10n.tip, tipDisplay),
                const SizedBox(height: 16),

                // Total
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      l10n.totalFare,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      totalFareDisplay,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildPriceRow(String label, String amount) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
        ),
        Text(
          amount,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildBottom() {
    return BlocBuilder<NewDeliveryCubit, NewDeliveryState>(
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.fromLTRB(20, 12, 20, 24),
          decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(
                color: Color(0xFFEBEBEB),
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              // Back button
              Expanded(
                child: SizedBox(
                  height: 48,
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: OutlinedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      side: BorderSide(color: Colors.grey[300]!),
                    ),
                    child: Text(
                      l10n.back,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // Confirm button
              Expanded(
                child: SizedBox(
                  height: 48,
                  child: ButtonLoading(
                    callback: () {
                      final newDelivery = state;

                      final timeZone =
                          context.read<AuthBloc>().state.businessTimeZone;
                      final scheduled = state.scheduledAt;
                      final scheduleTime = scheduled != null && scheduled != ''
                          ? DateTime.parse(scheduled)
                          : DateTime.now();
                      final scheduledTimeFormat = DateTimeHelper.convertToUtc(scheduleTime, timeZone ?? 'UTC')?.toIso8601String();
                      final oid = widget.order.id ?? widget.order.metadata?.orderId ?? '';

                      final body = {
                        'deliveryType': newDelivery.deliveryType,
                        'dropoffOption': newDelivery.dropoffOption,
                        'deliveryNotes': newDelivery.locationNotes?.value,
                        'scheduledAt': scheduled != null && scheduled != ''
                            ? scheduledTimeFormat
                            : null,
                        'dropoff': {
                          'street': newDelivery.place?.long,
                          'city': newDelivery.place?.city,
                          'country': newDelivery.place?.country,
                          'zipCode': newDelivery.place?.postalCode,
                          'state': newDelivery.place?.state,
                          'notes': newDelivery.deliveryNotes?.value,
                          'name': newDelivery.name?.value,
                          'phone':
                              '+${newDelivery.currentCode}${newDelivery.busPhone.value}',
                        },
                        'tip': newDelivery.tipAmount ?? 0,
                      };
                      // AppLog.d('phone = ${newDelivery.busPhone.value} / name = ${newDelivery.name} / address = ${newDelivery.deliveryAddress} / tip = ${newDelivery.tip}');
                      AppLog.d('body == ${jsonEncode(body)}');
                      if (customAmountFocusNode.hasFocus) {
                        customAmountFocusNode.unfocus();
                      }
                      if (oid == '') {
                        context.read<NewDeliveryCubit>().onCreateNewDeliveryNoneOrder(body: body);
                      } else {
                        context.read<NewDeliveryCubit>().onCreateNewDelivery(
                          oid: widget.order.id ?? widget.order.metadata?.orderId ?? '',
                          body: body,
                        );
                      }
                    },
                    label: l10n.confirm,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    buttonBackgroundColor: Colors.black,
                    labelColor: Colors.white,
                    borderRadius: 12,
                    isLoading: state.status == NewDeliveryStatus.Loading,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  double _getTipAmountValue(String tip) {
    switch (tip) {
      case '1':
        return 1;
      case '3':
        return 3;
      case '5':
        return 5;
      default:
        return 0;
    }
  }
}
